import dotenv from 'dotenv';
import path from 'path';

// Load environment variables FIRST
const result = dotenv.config({ path: path.resolve(process.cwd(), '.env') });
console.log('Dotenv result:', result);
console.log('Current working directory:', process.cwd());

// Manually set environment variables as fallback
if (!process.env.MONGODB_URI) {
  process.env.MONGODB_URI = 'mongodb+srv://admin:<EMAIL>/datvexemphim';
}
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development';
}
if (!process.env.JWT_SECRET) {
  process.env.JWT_SECRET = 'your-secret-key-change-this-in-production';
}

console.log('Environment variables loaded:');
console.log('MONGODB_URI:', process.env.MONGODB_URI ? 'SET' : 'NOT SET');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'SET' : 'NOT SET');

// Now import other modules that depend on environment variables
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

// Initialize the app first
async function startServer() {
  // Dynamic import of storage after env vars are loaded
  const { storage } = await import("./storage");

  const app = express();
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Connect to MongoDB
  try {
    await storage.connect();
    log('MongoDB connected successfully');
  } catch (error) {
    log('Failed to connect to MongoDB: ' + (error as Error).message);
    process.exit(1);
  }

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = process.env.PORT ? parseInt(process.env.PORT) : 5000;
  const host = process.platform === 'win32' ? 'localhost' : '0.0.0.0';

  server.listen(port, host, () => {
    log(`serving on ${host}:${port}`);
  }).on('error', (err: any) => {
    if (err.code === 'EADDRINUSE') {
      log(`Port ${port} is already in use. Please stop the existing process or use a different port.`);
      log(`To kill process on port ${port}, run: netstat -ano | findstr :${port} then taskkill /PID <PID> /F`);
      process.exit(1);
    } else {
      throw err;
    }
  });

  // Graceful shutdown
  process.on('SIGINT', async () => {
    log('Shutting down gracefully...');
    await storage.disconnect();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    log('Shutting down gracefully...');
    await storage.disconnect();
    process.exit(0);
  });
}

// Start the server
startServer().catch(console.error);
